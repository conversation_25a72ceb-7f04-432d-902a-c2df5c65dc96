### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RenderObject
  commentId: T:DrawnUi.Draw.RenderObject
  id: RenderObject
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RenderObject.Cache
  - DrawnUi.Draw.RenderObject.ClippingPath
  - DrawnUi.Draw.RenderObject.DelegateDrawCache
  - DrawnUi.Draw.RenderObject.Dispose
  - DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  - DrawnUi.Draw.RenderObject.EffectPostRenderer
  - DrawnUi.Draw.RenderObject.IsDistorted
  - DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  - DrawnUi.Draw.RenderObject.WillClipBounds
  langs:
  - csharp
  - vb
  name: RenderObject
  nameWithType: RenderObject
  fullName: DrawnUi.Draw.RenderObject
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RenderObject
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 136
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class RenderObject
    content.vb: Public Class RenderObject
  inheritance:
  - System.Object
  derivedClasses:
  - DrawnUi.Draw.RenderLabel
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RenderObject.Dispose
  commentId: M:DrawnUi.Draw.RenderObject.Dispose
  id: Dispose
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: RenderObject.Dispose()
  fullName: DrawnUi.Draw.RenderObject.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 139
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.RenderObject.Dispose*
- uid: DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  commentId: P:DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  id: ShouldClipAntialiased
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: ShouldClipAntialiased
  nameWithType: RenderObject.ShouldClipAntialiased
  fullName: DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ShouldClipAntialiased
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 144
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool ShouldClipAntialiased { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property ShouldClipAntialiased As Boolean
  overload: DrawnUi.Draw.RenderObject.ShouldClipAntialiased*
- uid: DrawnUi.Draw.RenderObject.ClippingPath
  commentId: P:DrawnUi.Draw.RenderObject.ClippingPath
  id: ClippingPath
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: ClippingPath
  nameWithType: RenderObject.ClippingPath
  fullName: DrawnUi.Draw.RenderObject.ClippingPath
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ClippingPath
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 145
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPath ClippingPath { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPath
    content.vb: Public Property ClippingPath As SKPath
  overload: DrawnUi.Draw.RenderObject.ClippingPath*
- uid: DrawnUi.Draw.RenderObject.Cache
  commentId: P:DrawnUi.Draw.RenderObject.Cache
  id: Cache
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: Cache
  nameWithType: RenderObject.Cache
  fullName: DrawnUi.Draw.RenderObject.Cache
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cache
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 146
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public CachedObject Cache { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.CachedObject
    content.vb: Public Property Cache As CachedObject
  overload: DrawnUi.Draw.RenderObject.Cache*
- uid: DrawnUi.Draw.RenderObject.IsDistorted
  commentId: P:DrawnUi.Draw.RenderObject.IsDistorted
  id: IsDistorted
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: IsDistorted
  nameWithType: RenderObject.IsDistorted
  fullName: DrawnUi.Draw.RenderObject.IsDistorted
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsDistorted
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 147
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsDistorted { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsDistorted As Boolean
  overload: DrawnUi.Draw.RenderObject.IsDistorted*
- uid: DrawnUi.Draw.RenderObject.WillClipBounds
  commentId: P:DrawnUi.Draw.RenderObject.WillClipBounds
  id: WillClipBounds
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: WillClipBounds
  nameWithType: RenderObject.WillClipBounds
  fullName: DrawnUi.Draw.RenderObject.WillClipBounds
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WillClipBounds
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 148
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool WillClipBounds { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property WillClipBounds As Boolean
  overload: DrawnUi.Draw.RenderObject.WillClipBounds*
- uid: DrawnUi.Draw.RenderObject.EffectPostRenderer
  commentId: P:DrawnUi.Draw.RenderObject.EffectPostRenderer
  id: EffectPostRenderer
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: EffectPostRenderer
  nameWithType: RenderObject.EffectPostRenderer
  fullName: DrawnUi.Draw.RenderObject.EffectPostRenderer
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EffectPostRenderer
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 149
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public IPostRendererEffect EffectPostRenderer { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.IPostRendererEffect
    content.vb: Public Property EffectPostRenderer As IPostRendererEffect
  overload: DrawnUi.Draw.RenderObject.EffectPostRenderer*
- uid: DrawnUi.Draw.RenderObject.DelegateDrawCache
  commentId: P:DrawnUi.Draw.RenderObject.DelegateDrawCache
  id: DelegateDrawCache
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: DelegateDrawCache
  nameWithType: RenderObject.DelegateDrawCache
  fullName: DrawnUi.Draw.RenderObject.DelegateDrawCache
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DelegateDrawCache
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 151
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Action<DrawingContext, CachedObject> DelegateDrawCache { get; set; }
    parameters: []
    return:
      type: System.Action{DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject}
    content.vb: Public Property DelegateDrawCache As Action(Of DrawingContext, CachedObject)
  overload: DrawnUi.Draw.RenderObject.DelegateDrawCache*
- uid: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  commentId: M:DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  id: DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  parent: DrawnUi.Draw.RenderObject
  langs:
  - csharp
  - vb
  name: DrawRenderObject(DrawingContext, CachedObject)
  nameWithType: RenderObject.DrawRenderObject(DrawingContext, CachedObject)
  fullName: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext, DrawnUi.Draw.CachedObject)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawRenderObject
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 153
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual void DrawRenderObject(DrawingContext ctx, CachedObject cache)
    parameters:
    - id: ctx
      type: DrawnUi.Draw.DrawingContext
    - id: cache
      type: DrawnUi.Draw.CachedObject
    content.vb: Public Overridable Sub DrawRenderObject(ctx As DrawingContext, cache As CachedObject)
  overload: DrawnUi.Draw.RenderObject.DrawRenderObject*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RenderObject.Dispose*
  commentId: Overload:DrawnUi.Draw.RenderObject.Dispose
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Dispose
  name: Dispose
  nameWithType: RenderObject.Dispose
  fullName: DrawnUi.Draw.RenderObject.Dispose
- uid: DrawnUi.Draw.RenderObject.ShouldClipAntialiased*
  commentId: Overload:DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_ShouldClipAntialiased
  name: ShouldClipAntialiased
  nameWithType: RenderObject.ShouldClipAntialiased
  fullName: DrawnUi.Draw.RenderObject.ShouldClipAntialiased
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.RenderObject.ClippingPath*
  commentId: Overload:DrawnUi.Draw.RenderObject.ClippingPath
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_ClippingPath
  name: ClippingPath
  nameWithType: RenderObject.ClippingPath
  fullName: DrawnUi.Draw.RenderObject.ClippingPath
- uid: SkiaSharp.SKPath
  commentId: T:SkiaSharp.SKPath
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  name: SKPath
  nameWithType: SKPath
  fullName: SkiaSharp.SKPath
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.RenderObject.Cache*
  commentId: Overload:DrawnUi.Draw.RenderObject.Cache
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Cache
  name: Cache
  nameWithType: RenderObject.Cache
  fullName: DrawnUi.Draw.RenderObject.Cache
- uid: DrawnUi.Draw.CachedObject
  commentId: T:DrawnUi.Draw.CachedObject
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.CachedObject.html
  name: CachedObject
  nameWithType: CachedObject
  fullName: DrawnUi.Draw.CachedObject
- uid: DrawnUi.Draw.RenderObject.IsDistorted*
  commentId: Overload:DrawnUi.Draw.RenderObject.IsDistorted
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_IsDistorted
  name: IsDistorted
  nameWithType: RenderObject.IsDistorted
  fullName: DrawnUi.Draw.RenderObject.IsDistorted
- uid: DrawnUi.Draw.RenderObject.WillClipBounds*
  commentId: Overload:DrawnUi.Draw.RenderObject.WillClipBounds
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_WillClipBounds
  name: WillClipBounds
  nameWithType: RenderObject.WillClipBounds
  fullName: DrawnUi.Draw.RenderObject.WillClipBounds
- uid: DrawnUi.Draw.RenderObject.EffectPostRenderer*
  commentId: Overload:DrawnUi.Draw.RenderObject.EffectPostRenderer
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_EffectPostRenderer
  name: EffectPostRenderer
  nameWithType: RenderObject.EffectPostRenderer
  fullName: DrawnUi.Draw.RenderObject.EffectPostRenderer
- uid: DrawnUi.Draw.IPostRendererEffect
  commentId: T:DrawnUi.Draw.IPostRendererEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IPostRendererEffect.html
  name: IPostRendererEffect
  nameWithType: IPostRendererEffect
  fullName: DrawnUi.Draw.IPostRendererEffect
- uid: DrawnUi.Draw.RenderObject.DelegateDrawCache*
  commentId: Overload:DrawnUi.Draw.RenderObject.DelegateDrawCache
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DelegateDrawCache
  name: DelegateDrawCache
  nameWithType: RenderObject.DelegateDrawCache
  fullName: DrawnUi.Draw.RenderObject.DelegateDrawCache
- uid: System.Action{DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject}
  commentId: T:System.Action{DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<DrawingContext, CachedObject>
  nameWithType: Action<DrawingContext, CachedObject>
  fullName: System.Action<DrawnUi.Draw.DrawingContext, DrawnUi.Draw.CachedObject>
  nameWithType.vb: Action(Of DrawingContext, CachedObject)
  fullName.vb: System.Action(Of DrawnUi.Draw.DrawingContext, DrawnUi.Draw.CachedObject)
  name.vb: Action(Of DrawingContext, CachedObject)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.CachedObject
    name: CachedObject
    href: DrawnUi.Draw.CachedObject.html
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.CachedObject
    name: CachedObject
    href: DrawnUi.Draw.CachedObject.html
  - name: )
- uid: System.Action`2
  commentId: T:System.Action`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T1, T2>
  nameWithType: Action<T1, T2>
  fullName: System.Action<T1, T2>
  nameWithType.vb: Action(Of T1, T2)
  fullName.vb: System.Action(Of T1, T2)
  name.vb: Action(Of T1, T2)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
- uid: DrawnUi.Draw.RenderObject.DrawRenderObject*
  commentId: Overload:DrawnUi.Draw.RenderObject.DrawRenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_
  name: DrawRenderObject
  nameWithType: RenderObject.DrawRenderObject
  fullName: DrawnUi.Draw.RenderObject.DrawRenderObject
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
