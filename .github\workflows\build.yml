name: build

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4
    - name: Setup .NET
      uses: actions/setup-dotnet@v2
      with:
        dotnet-version: 9.0.x
        
    - name: Install MAUI workload
      run: dotnet workload install maui
      
    - name: Build
      run: dotnet build src\Maui\DrawnUi\DrawnUi.Maui.csproj
     
