### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.Snapping
  commentId: T:DrawnUi.Draw.Snapping
  id: Snapping
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.Snapping.SnapPixelsToPixel(System.Single,System.Single)
  - DrawnUi.Draw.Snapping.SnapPointsToPixel(System.Single,System.Single,System.Double)
  langs:
  - csharp
  - vb
  name: Snapping
  nameWithType: Snapping
  fullName: DrawnUi.Draw.Snapping
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Snapping
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 7619
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static class Snapping
    content.vb: Public Module Snapping
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.Snapping.SnapPointsToPixel(System.Single,System.Single,System.Double)
  commentId: M:DrawnUi.Draw.Snapping.SnapPointsToPixel(System.Single,System.Single,System.Double)
  id: SnapPointsToPixel(System.Single,System.Single,System.Double)
  parent: DrawnUi.Draw.Snapping
  langs:
  - csharp
  - vb
  name: SnapPointsToPixel(float, float, double)
  nameWithType: Snapping.SnapPointsToPixel(float, float, double)
  fullName: DrawnUi.Draw.Snapping.SnapPointsToPixel(float, float, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SnapPointsToPixel
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 7628
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Used by the layout system to round a position translation value applying scale and initial anchor. Pass POINTS only, it wont do its job when receiving pixels!
  example: []
  syntax:
    content: public static float SnapPointsToPixel(float initialPosition, float translation, double scale)
    parameters:
    - id: initialPosition
      type: System.Single
      description: ''
    - id: translation
      type: System.Single
      description: ''
    - id: scale
      type: System.Double
      description: ''
    return:
      type: System.Single
      description: ''
    content.vb: Public Shared Function SnapPointsToPixel(initialPosition As Single, translation As Single, scale As Double) As Single
  overload: DrawnUi.Draw.Snapping.SnapPointsToPixel*
  nameWithType.vb: Snapping.SnapPointsToPixel(Single, Single, Double)
  fullName.vb: DrawnUi.Draw.Snapping.SnapPointsToPixel(Single, Single, Double)
  name.vb: SnapPointsToPixel(Single, Single, Double)
- uid: DrawnUi.Draw.Snapping.SnapPixelsToPixel(System.Single,System.Single)
  commentId: M:DrawnUi.Draw.Snapping.SnapPixelsToPixel(System.Single,System.Single)
  id: SnapPixelsToPixel(System.Single,System.Single)
  parent: DrawnUi.Draw.Snapping
  langs:
  - csharp
  - vb
  name: SnapPixelsToPixel(float, float)
  nameWithType: Snapping.SnapPixelsToPixel(float, float)
  fullName: DrawnUi.Draw.Snapping.SnapPixelsToPixel(float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SnapPixelsToPixel
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 7650
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static float SnapPixelsToPixel(float initialPosition, float translation)
    parameters:
    - id: initialPosition
      type: System.Single
    - id: translation
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Shared Function SnapPixelsToPixel(initialPosition As Single, translation As Single) As Single
  overload: DrawnUi.Draw.Snapping.SnapPixelsToPixel*
  nameWithType.vb: Snapping.SnapPixelsToPixel(Single, Single)
  fullName.vb: DrawnUi.Draw.Snapping.SnapPixelsToPixel(Single, Single)
  name.vb: SnapPixelsToPixel(Single, Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.Snapping.SnapPointsToPixel*
  commentId: Overload:DrawnUi.Draw.Snapping.SnapPointsToPixel
  href: DrawnUi.Draw.Snapping.html#DrawnUi_Draw_Snapping_SnapPointsToPixel_System_Single_System_Single_System_Double_
  name: SnapPointsToPixel
  nameWithType: Snapping.SnapPointsToPixel
  fullName: DrawnUi.Draw.Snapping.SnapPointsToPixel
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.Snapping.SnapPixelsToPixel*
  commentId: Overload:DrawnUi.Draw.Snapping.SnapPixelsToPixel
  href: DrawnUi.Draw.Snapping.html#DrawnUi_Draw_Snapping_SnapPixelsToPixel_System_Single_System_Single_
  name: SnapPixelsToPixel
  nameWithType: Snapping.SnapPixelsToPixel
  fullName: DrawnUi.Draw.Snapping.SnapPixelsToPixel
