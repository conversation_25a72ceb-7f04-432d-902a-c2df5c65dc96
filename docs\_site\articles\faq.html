<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Frequently Asked Questions | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Frequently Asked Questions | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/drawnui/blob/master/docs/articles/faq.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="frequently-asked-questions">Frequently Asked Questions</h1>

<h2 id="-onboarding">🤔 Onboarding</h2>
<p><strong>Q: What is the difference between DrawnUi and other drawn frameworks?</strong><br>
A: Not really comparable since DrawnUI is just a library for <strong>.NET MAUI</strong>, to let you draw UI instead of using native views.</p>
<p><strong>Q: Why choose drawn over native UI?</strong><br>
A: Rather a freedom choice to draw what you want and how you see it.<br>
It also can bemore performant to draw a complex UI on just one canvas instead of composing it with many native views.</p>
<p><strong>Q: Do I need to know how to draw on a canvas??</strong><br>
A: No, you can start by using prebuilt drawn controls and customize them. All controls are initially designed to be subclassed, customized, and almost every method is virtual.</p>
<p><strong>Q: Can I still use XAML?</strong><br>
A: Yes you can use both XAML and code-behind to create your UI.</p>
<p><strong>Q: Can I avoid using XAML at all costs?</strong><br>
A: Yes feel free to use code-behind to create your UI, up to using background thread to access and modify drawn controls properties.</p>
<p><strong>Q: How do I create custom controls with DrawnUI?</strong><br>
A: Inherit from <code>SkiaControl</code> for basic controls or <code>SkiaLayout</code> for containers etc. Override the <code>Paint</code> method to draw with SkiaSharp.</p>
<p><strong>Q: Can I embed native MAUI controls inside DrawnUI?</strong><br>
A: Yes! Use <code>SkiaMauiElement</code> to embed native MAUI controls like WebView inside your DrawnUI canvas. This allows you to combine the best of both worlds.</p>
<p><strong>Q: Possible to create a game with DrawnUI?</strong><br>
A: Well, since you draw, why not just draw a game instead of a business app. DrawnUI comes with gaming helpers and custom accelerated platform views to assure a smooth display-synched rendering.</p>
<h2 id="thechnical-questions">Thechnical Questions</h2>
<p><strong>Q: How do I create custom controls with DrawnUI?</strong><br>
A: Subclass <code>SkiaControl</code> for custom, <code>SkiaLayout</code> for container etc, . Override the <code>Paint</code> method to draw with SkiaSharp on the canvas provided inside drawing context.</p>
<p><strong>Q: Can I embed native MAUI controls inside DrawnUI?</strong><br>
A: Yes! Use <code>SkiaMauiElement</code> to embed native MAUI controls like WebView inside your DrawnUI canvas. This allows you to combine the best of both worlds.</p>
<p><strong>Q: Can I use MAUI's default <code>Resources/Images</code> folder?</strong><br>
A: Sorry, no, drawn resources lives inside <code>Resources/Raw</code> and subfolders.</p>
<p><strong>Q: How do I change SkiaSvg source not from file/url?</strong><br>
A: set <code>SvgString</code> property to svg text string.</p>
<p><strong>Q: Why my scroll is resetting at all time while Iproperly use ObservableCollection for LoadMore?</strong><br>
A: Check that your custom ObservableRangeCollection is sending Reset event when adding range of items.</p>
<p><strong>Q: How do I change SkiaImage source not from file/url?</strong><br>
A: set directly: <code>mySkiaImage.SetImageInternal(skiaImage)</code>.</p>
<p><strong>Q: How do I prevent touch events from passing through overlapping controls?</strong><br>
A: Use the <code>BlockGesturesBelow=&quot;True&quot;</code> property on the top control. Note that <code>InputTransparent</code> makes the control itself avoid gestures, not controls below.</p>
<p><strong>Q: How do I internally rebuild the ItemsSource?</strong><br>
A: Directly call <code>layout.ApplyItemsSource()</code>.</p>
<h2 id="troubleshooting">Troubleshooting</h2>
<p><strong>Q: Why is scrolling/rendering slow?</strong></p>
<p><strong>Solutions:</strong></p>
<ol>
<li>Always use cache for layers of controls:
<ul>
<li>Do NOT cache scrolls/heavily animated controls and above</li>
<li><code>UseCache = SkiaCacheType.Operations</code> for labels and svg</li>
<li><code>UseCache = SkiaCacheType.Image</code> for complex layouts, buttons etc</li>
<li><code>UseCache = SkiaCacheType.ImageComposite</code> for complex layouts where a region changes while others remain static, like a stack with different user-handled controls.</li>
<li><code>UseCache = SkiaCacheType.ImageDoubleBuffered</code> for equally sized recycled cells. Will show old cache while preparing new one in background.</li>
<li><code>UseCache = SkiaCacheType.GPU</code> for small static overlays like headers, navbars.</li>
</ul>
</li>
<li>Check that you do not have some logging running for every rendering frame.</li>
</ol>
<p><strong>Q: Why isn't my UI updating when ViewModel properties change:</strong></p>
<p><strong>Solutions:</strong></p>
<ol>
<li>Ensure ViewModel implements <code>INotifyPropertyChanged</code></li>
<li>Check that property either has a static bindable property or calls <code>OnPropertyChanged()</code> in the setter.</li>
<li>Check that property names match exactly; use <code>nameof()</code>.</li>
<li>Ensure all your overrides, if any, of <code>void OnPropertyChanged([CallerMemberName] string propertyName = null)</code> have a <code>[CallerMemberName]</code> attribute.</li>
</ol>
<hr>
<p><strong>Can't find the answer to your question?</strong> →</p>
<ul>
<li>Please check out <a href="samples.html">samples source code</a>, covers many scenarios.</li>
<li><a href="https://github.com/taublast/DrawnUi/discussions">Ask in GitHub Discussions</a>** - The community is here to help!</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/drawnui/blob/master/docs/articles/faq.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>


    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
