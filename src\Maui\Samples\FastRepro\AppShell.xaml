<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="Sandbox.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:sandbox="clr-namespace:Sandbox"
    Title="DrawnUI for .NET MAUI"
    Shell.FlyoutBehavior="Disabled">

    <!--<ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate sandbox:PlanesTest}"
        Route="MainPage" />-->

    <!--<ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate sandbox:MainPageCode}"
        Route="MainPage" />-->

    <!--<ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate sandbox:TutorialCards}"
        Route="MainPage" />-->

    <ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate sandbox:MainPage}"
        Route="MainPage" />

</Shell>
